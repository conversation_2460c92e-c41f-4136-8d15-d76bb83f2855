"""
S参数仿真引擎
基于传输矩阵方法实现微带线滤波器的S参数计算
"""

import numpy as np
import math
from typing import List, Tuple, Dict, Optional
from dataclasses import dataclass
from microstrip_design import MicrostripSegment, MicrostripDesigner
from filter_theory import SubstrateParams


@dataclass
class SParameters:
    """S参数结果"""
    frequencies: np.ndarray  # 频率数组 (Hz)
    s11: np.ndarray  # 反射系数
    s21: np.ndarray  # 传输系数
    s12: np.ndarray  # 反向传输系数
    s22: np.ndarray  # 输出反射系数
    
    def get_insertion_loss_db(self) -> np.ndarray:
        """获取插入损耗 (dB)"""
        return -20 * np.log10(np.abs(self.s21))
    
    def get_return_loss_db(self) -> np.ndarray:
        """获取回波损耗 (dB)"""
        return -20 * np.log10(np.abs(self.s11))
    
    def get_vswr(self) -> np.ndarray:
        """获取电压驻波比"""
        s11_mag = np.abs(self.s11)
        return (1 + s11_mag) / (1 - s11_mag)


class TransmissionMatrix:
    """传输矩阵类"""
    
    def __init__(self, matrix: np.ndarray):
        """
        初始化传输矩阵
        matrix: 2x2复数矩阵 [[A, B], [C, D]]
        """
        self.matrix = matrix
    
    @property
    def A(self) -> complex:
        return self.matrix[0, 0]
    
    @property
    def B(self) -> complex:
        return self.matrix[0, 1]
    
    @property
    def C(self) -> complex:
        return self.matrix[1, 0]
    
    @property
    def D(self) -> complex:
        return self.matrix[1, 1]
    
    def cascade(self, other: 'TransmissionMatrix') -> 'TransmissionMatrix':
        """级联两个传输矩阵"""
        result = np.dot(self.matrix, other.matrix)
        return TransmissionMatrix(result)
    
    def to_s_parameters(self, z0: float = 50.0) -> Tuple[complex, complex, complex, complex]:
        """转换为S参数"""
        A, B, C, D = self.A, self.B, self.C, self.D
        
        # 传输矩阵到S参数的转换公式
        denominator = A + B/z0 + C*z0 + D
        
        s11 = (A + B/z0 - C*z0 - D) / denominator
        s12 = 2 * (A*D - B*C) / denominator
        s21 = 2 / denominator
        s22 = (-A + B/z0 - C*z0 + D) / denominator
        
        return s11, s12, s21, s22


class MicrostripSimulator:
    """微带线仿真器"""
    
    def __init__(self, substrate: SubstrateParams):
        self.substrate = substrate
        self.c0 = 3e8  # 光速
        
    def calculate_propagation_constant(self, segment: MicrostripSegment, freq: float) -> complex:
        """计算传播常数"""
        # 相位常数
        lambda_0 = self.c0 / freq
        lambda_g = lambda_0 / math.sqrt(segment.eff_er)
        beta = 2 * math.pi / lambda_g
        
        # 衰减常数 (Np/m)
        alpha_total = (segment.alpha_d + segment.alpha_c) / 8.686  # 转换为Np/m
        
        # 复传播常数
        gamma = alpha_total + 1j * beta
        
        return gamma
    
    def transmission_matrix_segment(self, segment: MicrostripSegment, freq: float) -> TransmissionMatrix:
        """计算单个微带线段的传输矩阵"""
        gamma = self.calculate_propagation_constant(segment, freq)
        z0 = segment.z0
        length = segment.length
        
        # 传输矩阵元素
        cosh_gl = np.cosh(gamma * length)
        sinh_gl = np.sinh(gamma * length)
        
        A = cosh_gl
        B = z0 * sinh_gl
        C = sinh_gl / z0
        D = cosh_gl
        
        matrix = np.array([[A, B], [C, D]], dtype=complex)
        return TransmissionMatrix(matrix)
    
    def transmission_matrix_discontinuity(self, z1: float, z2: float) -> TransmissionMatrix:
        """计算阻抗不连续的传输矩阵"""
        # 简化的不连续模型（忽略寄生效应）
        # 实际应用中可能需要更复杂的模型
        A = 1.0
        B = 0.0
        C = 0.0
        D = 1.0
        
        matrix = np.array([[A, B], [C, D]], dtype=complex)
        return TransmissionMatrix(matrix)
    
    def simulate_filter(self, segments: List[MicrostripSegment], 
                       frequencies: np.ndarray, z0_system: float = 50.0) -> SParameters:
        """仿真整个滤波器"""
        n_freq = len(frequencies)
        s11 = np.zeros(n_freq, dtype=complex)
        s12 = np.zeros(n_freq, dtype=complex)
        s21 = np.zeros(n_freq, dtype=complex)
        s22 = np.zeros(n_freq, dtype=complex)
        
        for i, freq in enumerate(frequencies):
            # 初始化总传输矩阵为单位矩阵
            total_matrix = TransmissionMatrix(np.eye(2, dtype=complex))
            
            # 级联所有段的传输矩阵
            for j, segment in enumerate(segments):
                # 微带线段传输矩阵
                segment_matrix = self.transmission_matrix_segment(segment, freq)
                total_matrix = total_matrix.cascade(segment_matrix)
                
                # 如果不是最后一段，添加不连续传输矩阵
                if j < len(segments) - 1:
                    next_segment = segments[j + 1]
                    discontinuity_matrix = self.transmission_matrix_discontinuity(
                        segment.z0, next_segment.z0)
                    total_matrix = total_matrix.cascade(discontinuity_matrix)
            
            # 转换为S参数
            s11[i], s12[i], s21[i], s22[i] = total_matrix.to_s_parameters(z0_system)
        
        return SParameters(frequencies, s11, s21, s12, s22)
    
    def analyze_performance(self, s_params: SParameters, specs) -> Dict[str, bool]:
        """分析滤波器性能是否满足规格"""
        freq = s_params.frequencies
        il_db = s_params.get_insertion_loss_db()
        rl_db = s_params.get_return_loss_db()
        
        # 通带性能检查
        pass_mask = (freq >= 0.1e9) & (freq <= specs.f_pass)
        pass_il_ok = np.all(il_db[pass_mask] <= specs.pass_loss_max)
        pass_rl_ok = np.all(rl_db[pass_mask] >= specs.pass_return_loss_min)
        
        # 阻带性能检查
        stop_mask = (freq >= specs.f_stop_start) & (freq <= specs.f_stop_end)
        stop_il_ok = np.all(il_db[stop_mask] >= specs.stop_loss_min)
        
        results = {
            'passband_insertion_loss': pass_il_ok,
            'passband_return_loss': pass_rl_ok,
            'stopband_insertion_loss': stop_il_ok,
            'overall_performance': pass_il_ok and pass_rl_ok and stop_il_ok
        }
        
        return results


class FrequencyDomainAnalysis:
    """频域分析工具"""
    
    @staticmethod
    def generate_frequency_points(f_start: float, f_stop: float, n_points: int = 1001, 
                                scale: str = 'linear') -> np.ndarray:
        """生成频率点"""
        if scale == 'linear':
            return np.linspace(f_start, f_stop, n_points)
        elif scale == 'log':
            return np.logspace(np.log10(f_start), np.log10(f_stop), n_points)
        else:
            raise ValueError("scale must be 'linear' or 'log'")
    
    @staticmethod
    def find_3db_bandwidth(frequencies: np.ndarray, s21_db: np.ndarray) -> Tuple[float, float]:
        """找到3dB带宽"""
        # 找到最大传输点
        max_idx = np.argmax(-s21_db)  # 最小插入损耗
        max_level = -s21_db[max_idx]
        
        # 3dB点
        target_level = max_level - 3.0
        
        # 找到左右3dB点
        left_idx = np.where((-s21_db[:max_idx]) >= target_level)[0]
        right_idx = np.where((-s21_db[max_idx:]) >= target_level)[0] + max_idx
        
        if len(left_idx) > 0 and len(right_idx) > 0:
            f_low = frequencies[left_idx[-1]]
            f_high = frequencies[right_idx[0]]
            return f_low, f_high
        else:
            return None, None
    
    @staticmethod
    def calculate_group_delay(frequencies: np.ndarray, s21: np.ndarray) -> np.ndarray:
        """计算群延迟"""
        phase = np.unwrap(np.angle(s21))
        omega = 2 * np.pi * frequencies
        
        # 数值微分计算群延迟
        group_delay = -np.gradient(phase, omega)
        
        return group_delay


def main():
    """主函数：演示S参数仿真功能"""
    from filter_theory import FilterSpecs, ChebyshevFilter
    
    # 创建设计参数
    specs = FilterSpecs()
    substrate = SubstrateParams()
    
    print("=== S参数仿真引擎测试 ===\n")
    
    # 获取滤波器设计
    filter_design = ChebyshevFilter(specs)
    filter_design.calculate_filter_order()
    impedances, lengths = filter_design.convert_to_impedances()
    
    # 设计微带线段
    designer = MicrostripDesigner(substrate)
    segments = designer.design_filter_segments(impedances, lengths, specs.f_pass)
    
    # 创建仿真器
    simulator = MicrostripSimulator(substrate)
    
    # 生成频率点
    frequencies = FrequencyDomainAnalysis.generate_frequency_points(
        0.1e9, 4.0e9, 1001)
    
    print("开始S参数仿真...")
    s_params = simulator.simulate_filter(segments, frequencies)
    
    # 分析性能
    performance = simulator.analyze_performance(s_params, specs)
    
    print("\n性能分析结果:")
    print(f"通带插入损耗: {'✓' if performance['passband_insertion_loss'] else '✗'}")
    print(f"通带回波损耗: {'✓' if performance['passband_return_loss'] else '✗'}")
    print(f"阻带插入损耗: {'✓' if performance['stopband_insertion_loss'] else '✗'}")
    print(f"总体性能: {'✓' if performance['overall_performance'] else '✗'}")
    
    # 关键频点性能
    f_test_points = [0.5e9, 1.0e9, 1.5e9, 2.0e9, 2.5e9, 3.0e9]
    print(f"\n关键频点性能:")
    for f_test in f_test_points:
        idx = np.argmin(np.abs(frequencies - f_test))
        il = s_params.get_insertion_loss_db()[idx]
        rl = s_params.get_return_loss_db()[idx]
        print(f"{f_test/1e9:.1f}GHz: IL={il:.2f}dB, RL={rl:.2f}dB")


if __name__ == "__main__":
    main()

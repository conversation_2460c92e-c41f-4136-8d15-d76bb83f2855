"""
微带线设计模块
实现微带线几何参数精确计算，包括线宽、线长、特性阻抗等
"""

import numpy as np
import math
from typing import Tuple, List, Dict, Optional
from dataclasses import dataclass
from filter_theory import SubstrateParams


@dataclass
class MicrostripSegment:
    """微带线段参数"""
    width: float  # 线宽 (m)
    length: float  # 物理长度 (m)
    z0: float  # 特性阻抗 (Ohm)
    electrical_length: float  # 电长度 (度)
    eff_er: float  # 有效介电常数
    alpha_d: float  # 介质损耗 (dB/m)
    alpha_c: float  # 导体损耗 (dB/m)


class MicrostripDesigner:
    """微带线设计器"""
    
    def __init__(self, substrate: SubstrateParams):
        self.substrate = substrate
        self.c0 = 3e8  # 光速 (m/s)
        
    def wheeler_impedance(self, w: float, h: float, er: float, t: float = 0) -> float:
        """Wheeler公式计算特性阻抗（考虑导体厚度）"""
        # 修正的宽度比
        if t > 0:
            # 考虑导体厚度的修正
            dw = t / math.pi * (1 + math.log(4 * math.pi * w / t)) if w / t > 1 / (2 * math.pi) else t / math.pi * (1 + math.log(2 * h / t))
            w_eff = w + dw
        else:
            w_eff = w
            
        u = w_eff / h
        
        # 有效介电常数
        if u <= 1:
            eff_er = (er + 1) / 2 + (er - 1) / 2 * (1 / math.sqrt(1 + 12 / u) + 0.04 * (1 - u)**2)
        else:
            eff_er = (er + 1) / 2 + (er - 1) / 2 / math.sqrt(1 + 12 / u)
        
        # 特性阻抗
        if u <= 1:
            z0 = 60 / math.sqrt(eff_er) * math.log(8 / u + u / 4)
        else:
            z0 = 120 * math.pi / math.sqrt(eff_er) / (u + 1.393 + 0.667 * math.log(u + 1.444))
            
        return z0, eff_er
    
    def hammerstad_jensen_impedance(self, w: float, h: float, er: float, t: float = 0) -> Tuple[float, float]:
        """Hammerstad-Jensen公式（更精确）"""
        # 导体厚度修正
        if t > 0:
            dt_h = t / h
            if w / h >= 1 / (2 * math.pi):
                dw_h = dt_h / math.pi * (1 + math.log(2 * h / t))
            else:
                dw_h = dt_h / math.pi * (1 + math.log(4 * math.pi * w / t))
            w_eff = w + dw_h * h
        else:
            w_eff = w
            
        u = w_eff / h
        
        # 有效介电常数（Hammerstad-Jensen）
        a = 1 + (1/49) * math.log((u**4 + (u/52)**2) / (u**4 + 0.432)) + (1/18.7) * math.log(1 + (u/18.1)**3)
        b = 0.564 * ((er - 0.9) / (er + 3))**0.053
        
        eff_er = (er + 1) / 2 + (er - 1) / 2 * (1 + 10/u)**(-a * b)
        
        # 特性阻抗
        f = 6 + (2 * math.pi - 6) * math.exp(-(30.666 / u)**0.7528)
        z0 = 60 / math.sqrt(eff_er) * math.log(f / u + math.sqrt(1 + (2/u)**2))
        
        return z0, eff_er
    
    def calculate_width_from_impedance(self, z0_target: float, method: str = 'hammerstad') -> float:
        """根据目标阻抗计算线宽"""
        h = self.substrate.h
        er = self.substrate.er
        t = self.substrate.t
        
        # 初始估计
        if z0_target > 60:
            # 高阻抗，窄线
            w_init = h * (8 * math.exp(z0_target * math.sqrt(er + 1.41) / 87) / 
                         (math.exp(z0_target * math.sqrt(er + 1.41) / 87) - 2))
        else:
            # 低阻抗，宽线
            A = z0_target / 60 * math.sqrt((er + 1) / 2) + (er - 1) / (er + 1) * (0.23 + 0.11 / er)
            w_init = h * (8 / math.exp(A) - 2) / (math.exp(A) - 2)
        
        # 牛顿迭代法精确求解
        w = w_init
        tolerance = 1e-8
        max_iter = 50
        
        for i in range(max_iter):
            if method == 'hammerstad':
                z0_calc, _ = self.hammerstad_jensen_impedance(w, h, er, t)
            else:
                z0_calc, _ = self.wheeler_impedance(w, h, er, t)
            
            error = z0_calc - z0_target
            if abs(error) < tolerance:
                break
                
            # 数值导数
            dw = w * 1e-6
            if method == 'hammerstad':
                z0_plus, _ = self.hammerstad_jensen_impedance(w + dw, h, er, t)
            else:
                z0_plus, _ = self.wheeler_impedance(w + dw, h, er, t)
            
            dz_dw = (z0_plus - z0_calc) / dw
            
            # 牛顿更新
            if abs(dz_dw) > 1e-12:
                w = w - error / dz_dw
                w = max(w, h * 0.01)  # 限制最小宽度
                w = min(w, h * 20)    # 限制最大宽度
        
        return w
    
    def calculate_physical_length(self, electrical_length_deg: float, freq: float, 
                                w: float, method: str = 'hammerstad') -> float:
        """计算物理长度"""
        h = self.substrate.h
        er = self.substrate.er
        t = self.substrate.t
        
        if method == 'hammerstad':
            _, eff_er = self.hammerstad_jensen_impedance(w, h, er, t)
        else:
            _, eff_er = self.wheeler_impedance(w, h, er, t)
        
        # 频率色散修正（Getsinger模型）
        f_te = self.c0 / (4 * h * math.sqrt(er - 1))  # TE10模式截止频率
        
        if freq < f_te:
            # 低频近似
            eff_er_f = eff_er
        else:
            # 频率色散修正
            u = w / h
            P1 = 0.27488 + (0.6315 + 0.525 / (1 + 0.0157 * freq / 1e9)**20) * u - 0.065683 * math.exp(-8.7513 * u)
            P2 = 0.33622 * (1 - math.exp(-0.03442 * er))
            P3 = 0.0363 * math.exp(-4.6 * u) * (1 - math.exp(-(freq / 1e9 / 3.87)**4.97))
            P4 = 1 + 2.751 * (1 - math.exp(-(er / 15.916)**8))
            P = P1 * P2 * ((0.1844 + P3 * P4) * freq / 1e9)**1.5763
            
            eff_er_f = er - (er - eff_er) / (1 + P)
        
        # 波长和物理长度
        lambda_0 = self.c0 / freq
        lambda_g = lambda_0 / math.sqrt(eff_er_f)
        length = electrical_length_deg / 360 * lambda_g
        
        return length
    
    def calculate_losses(self, w: float, freq: float, method: str = 'hammerstad') -> Tuple[float, float]:
        """计算损耗"""
        h = self.substrate.h
        er = self.substrate.er
        t = self.substrate.t
        
        if method == 'hammerstad':
            z0, eff_er = self.hammerstad_jensen_impedance(w, h, er, t)
        else:
            z0, eff_er = self.wheeler_impedance(w, h, er, t)
        
        # 介质损耗 (Np/m)
        k0 = 2 * math.pi * freq / self.c0
        alpha_d_np = k0 * eff_er * self.substrate.tan_delta / (2 * math.sqrt(eff_er))
        alpha_d_db = alpha_d_np * 8.686  # 转换为dB/m
        
        # 导体损耗 (Np/m)
        mu0 = 4 * math.pi * 1e-7
        sigma_cu = 5.8e7  # 铜的电导率 S/m
        Rs = math.sqrt(math.pi * freq * mu0 / sigma_cu)  # 表面电阻
        
        # 导体损耗系数（考虑电流分布）
        u = w / h
        if u >= 1:
            # 宽线情况
            alpha_c_np = Rs / (z0 * w) * (1 + h / w * (1 + 1.25 * t / (math.pi * h) * (1 + math.log(2 * h / t))))
        else:
            # 窄线情况
            alpha_c_np = Rs / (z0 * w) * (1 + h / w + t / (math.pi * w) * (1 + math.log(4 * math.pi * w / t)))
        
        alpha_c_db = alpha_c_np * 8.686  # 转换为dB/m
        
        return alpha_d_db, alpha_c_db
    
    def design_segment(self, z0_target: float, electrical_length_deg: float, 
                      freq: float, method: str = 'hammerstad') -> MicrostripSegment:
        """设计单个微带线段"""
        # 计算线宽
        width = self.calculate_width_from_impedance(z0_target, method)
        
        # 验证阻抗
        if method == 'hammerstad':
            z0_actual, eff_er = self.hammerstad_jensen_impedance(width, self.substrate.h, self.substrate.er, self.substrate.t)
        else:
            z0_actual, eff_er = self.wheeler_impedance(width, self.substrate.h, self.substrate.er, self.substrate.t)
        
        # 计算物理长度
        length = self.calculate_physical_length(electrical_length_deg, freq, width, method)
        
        # 计算损耗
        alpha_d, alpha_c = self.calculate_losses(width, freq, method)
        
        return MicrostripSegment(
            width=width,
            length=length,
            z0=z0_actual,
            electrical_length=electrical_length_deg,
            eff_er=eff_er,
            alpha_d=alpha_d,
            alpha_c=alpha_c
        )
    
    def design_filter_segments(self, impedances: List[float], electrical_lengths: List[float], 
                             freq: float, method: str = 'hammerstad') -> List[MicrostripSegment]:
        """设计滤波器所有段"""
        segments = []
        
        for i, (z0, length_deg) in enumerate(zip(impedances, electrical_lengths)):
            segment = self.design_segment(z0, length_deg, freq, method)
            segments.append(segment)
            
            print(f"段 {i+1}: Z0={segment.z0:.1f}Ω (目标{z0:.1f}Ω)")
            print(f"      W={segment.width*1000:.3f}mm, L={segment.length*1000:.3f}mm")
            print(f"      εeff={segment.eff_er:.3f}, αd={segment.alpha_d:.3f}dB/m, αc={segment.alpha_c:.3f}dB/m")
        
        return segments


def main():
    """主函数：演示微带线设计功能"""
    from filter_theory import FilterSpecs, ChebyshevFilter
    
    # 创建设计参数
    specs = FilterSpecs()
    substrate = SubstrateParams()
    
    print("=== 微带线设计模块测试 ===\n")
    
    # 获取滤波器设计参数
    filter_design = ChebyshevFilter(specs)
    filter_design.calculate_filter_order()
    impedances, lengths = filter_design.convert_to_impedances()
    
    # 创建微带线设计器
    designer = MicrostripDesigner(substrate)
    
    print("微带线段设计结果:")
    segments = designer.design_filter_segments(impedances, lengths, specs.f_pass)
    
    # 计算总尺寸
    total_length = sum(seg.length for seg in segments)
    max_width = max(seg.width for seg in segments)
    
    print(f"\n滤波器总体尺寸:")
    print(f"总长度: {total_length*1000:.3f} mm ({total_length/25.4:.3f} 英寸)")
    print(f"最大宽度: {max_width*1000:.3f} mm ({max_width/25.4:.3f} 英寸)")
    
    # 检查尺寸约束
    max_length_inch = 4.05
    max_width_inch = 1.1
    
    if total_length/25.4e-3 <= max_length_inch and max_width/25.4e-3 <= max_width_inch:
        print("✓ 满足尺寸约束")
    else:
        print("✗ 超出尺寸约束")


if __name__ == "__main__":
    main()
